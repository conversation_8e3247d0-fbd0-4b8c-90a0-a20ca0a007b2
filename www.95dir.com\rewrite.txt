# 301重定向：强制规范化URL格式，避免重复内容
# 重定向带.html后缀的首页到根目录
if ($uri = "/index.html") {
    return 301 /;
}

# 重定向其他带.html后缀的页面到无后缀版本
if ($uri ~ "^/(webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)\.html$") {
    return 301 /$1;
}

# 重定向带尾部斜杠的页面到无斜杠版本（除了首页）
if ($uri ~ "^/(webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)/$") {
    return 301 /$1;
}

# 首页、分类浏览、数据归档、最近更新、排行榜、意见反馈
if ($uri ~ "^/(webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)$") {
    rewrite ^/(.+)$ /index.php?mod=$1 last;
}

# 301重定向：规范化最近更新和数据归档URL格式
if ($uri ~ "^/(update|archives)(-|/)(\d+)(-|/)(\d+)\.html$") {
    return 301 /$1/$3/$5;
}
if ($uri ~ "^/(update|archives)(-|/)(\d+)(-|/)(\d+)/$") {
    return 301 /$1/$3/$5;
}
if ($uri ~ "^/(update|archives)(-|/)(\d+)\.html$") {
    return 301 /$1/$3;
}
if ($uri ~ "^/(update|archives)(-|/)(\d+)/$") {
    return 301 /$1/$3;
}

# 最近更新和数据归档
if ($uri ~ "^/(update|archives)/(\d+)/(\d+)$") {
    rewrite ^/(update|archives)/(\d+)/(\d+)$ /index.php?mod=$1&date=$2&page=$3 last;
}
if ($uri ~ "^/(update|archives)/(\d+)$") {
    rewrite ^/(update|archives)/(\d+)$ /index.php?mod=$1&date=$2 last;
}

# 301重定向：规范化搜索URL格式
if ($uri ~ "^/search(-|/)(name|url|tags|baidu|intro|br|pr|sr|article)(-|/)(.*)(-|/)?(\d+)?\.html$") {
    set $search_type $2;
    set $search_query $4;
    set $search_page $6;
    if ($search_page) {
        return 301 /search/$search_type/$search_query/$search_page;
    }
    return 301 /search/$search_type/$search_query;
}

# 站内搜索 - 注意：搜索页面应该使用noindex，避免被搜索引擎索引
if ($uri ~ "^/search/(name|url|tags|baidu|intro|br|pr|sr|article)/(.+)/(\d+)$") {
    rewrite ^/search/(name|url|tags|baidu|intro|br|pr|sr|article)/(.+)/(\d+)$ /index.php?mod=search&type=$1&query=$2&page=$3 last;
}
if ($uri ~ "^/search/(name|url|tags|baidu|intro|br|pr|sr|article)/(.+)$") {
    rewrite ^/search/(name|url|tags|baidu|intro|br|pr|sr|article)/(.+)$ /index.php?mod=search&type=$1&query=$2 last;
}

# 301重定向：规范化详情页URL格式
if ($uri ~ "^/(siteinfo|artinfo|linkinfo|diypage)(-|/)(\d+)\.html$") {
    return 301 /$1/$3;
}
if ($uri ~ "^/(siteinfo|artinfo|linkinfo|diypage)(-|/)(\d+)/$") {
    return 301 /$1/$3;
}

# 站点详细、文章详细、链接详细、单页
if ($uri ~ "^/(siteinfo|artinfo|linkinfo|diypage)/(\d+)$") {
    set $detail_mod $1;
    set $detail_id $2;
    # 根据不同类型使用不同的参数名
    if ($detail_mod = "siteinfo") {
        rewrite ^/siteinfo/(\d+)$ /index.php?mod=siteinfo&wid=$1 last;
    }
    if ($detail_mod = "artinfo") {
        rewrite ^/artinfo/(\d+)$ /index.php?mod=artinfo&aid=$1 last;
    }
    if ($detail_mod = "linkinfo") {
        rewrite ^/linkinfo/(\d+)$ /index.php?mod=linkinfo&lid=$1 last;
    }
    if ($detail_mod = "diypage") {
        rewrite ^/diypage/(\d+)$ /index.php?mod=diypage&pid=$1 last;
    }
}

# 301重定向：规范化RSS和SiteMap URL格式
if ($uri ~ "^/rssfeed(-|/)(.+)(-|/)?(\d+)?\.html$") {
    set $rss_type $2;
    set $rss_cid $4;
    if ($rss_cid) {
        return 301 /rssfeed/$rss_type/$rss_cid;
    }
    return 301 /rssfeed/$rss_type;
}
if ($uri ~ "^/sitemap(-|/)(.+)\.html$") {
    return 301 /sitemap/$2;
}

# RSS
if ($uri ~ "^/rssfeed/(.+)/(\d+)$") {
    rewrite ^/rssfeed/(.+)/(\d+)$ /index.php?mod=rssfeed&type=$1&cid=$2 last;
}
if ($uri ~ "^/rssfeed/(.+)$") {
    rewrite ^/rssfeed/(.+)$ /index.php?mod=rssfeed&type=$1 last;
}

# SiteMap
if ($uri ~ "^/sitemap/(.+)$") {
    rewrite ^/sitemap/(.+)$ /index.php?mod=sitemap&cid=$1 last;
}

# 301重定向：规范化分类目录URL格式
if ($uri ~ "^/(webdir|article)(-|/)(.+)(-|/)(\d+)(-|/)(\d+)\.html$") {
    return 301 /$1/$3/$5/$7;
}
if ($uri ~ "^/(webdir|article)(-|/)(.+)(-|/)(\d+)(-|/)(\d+)/$") {
    return 301 /$1/$3/$5/$7;
}
if ($uri ~ "^/(webdir|article)(-|/)(.+)(-|/)(\d+)\.html$") {
    return 301 /$1/$3/$5;
}
if ($uri ~ "^/(webdir|article)(-|/)(.+)(-|/)(\d+)/$") {
    return 301 /$1/$3/$5;
}

# 分类目录
if ($uri ~ "^/(webdir|article)/(.+)/(\d+)/(\d+)$") {
    rewrite ^/(webdir|article)/(.+)/(\d+)/(\d+)$ /index.php?mod=$1&cid=$3&page=$4 last;
}
if ($uri ~ "^/(webdir|article)/(.+)/(\d+)$") {
    rewrite ^/(webdir|article)/(.+)/(\d+)$ /index.php?mod=$1&cid=$3 last;
}

# 301重定向：处理查询参数顺序问题，确保规范化
# 重定向带有多余参数的URL到规范格式
if ($args ~ "^mod=([^&]+)&(.+)$") {
    set $clean_mod $1;
    set $other_args $2;
    # 如果是简单的mod参数，重定向到干净的URL
    if ($clean_mod ~ "^(index|webdir|weblink|article|category|update|archives|top|feedback|link)$") {
        if ($clean_mod = "index") {
            return 301 /;
        }
        return 301 /$clean_mod;
    }
}

# 强制HTTPS重定向（如果需要）
# if ($scheme != "https") {
#     return 301 https://$server_name$request_uri;
# }

# PHP 处理
if (!-e $request_filename) {
    rewrite ^ /index.php last;
}