<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<meta name="robots" content="noindex,follow" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />

<!-- SEO优化 - 规范链接，但不索引提交表单页面 -->
<link rel="canonical" href="{#$site_url#}?mod=addurl" />

<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}

	<div id="mainbox" class="clearfix mtop10">
		<div id="subbox">
		<div style="line-height: 25px; padding: 10px 20px;">
        <strong style="color: #f00;">提交须知：</strong><br />
1. 不收录有反动、色情、赌博等不良内容或提供不良内容链接的网站，以及网站名称或内容违反国家有关政策法规的网站；<br />
2. 不收录含有病毒、木马，弹出插件或恶意更改他人电脑设置的网站、及有多个弹窗广告的网站；<br />
3. 不收录网站名称和实际内容不符的网站，如贵站正在建设中，或尚未明确主题的网站，请不必现在申请收录，欢迎您在贵站建设完毕后再申请；<br />
4. 不收录以同类型网站通用名称文字作为申请的名称，例如"在线音乐"，请以适当的网站名做为申请名称；<br />
5. 不收入非顶级域名、挂靠其他站点、无实际内容，只提供域名指向的网站或仅有单页内容的网站；<br />
6. 不收录在正常情况下无法访问的网站；<br />
7. 公益性网站，或内容确实有独特之处的网站将优先收录；<br />
<span style="color: #f00;"><strong>特别提示：</strong><br />
为了达到共赢的效果，凡是申请收录的网站，请在其首页设置 "优站分类目录" 的文字或图片友情链接；<br />
本站会定期对已收录网站进行复审，对不符合要求（包括短期链接欺骗收录行为）的网站，本站将随时删除该站信息。</span><br />
			<table width="100%" style="background: #FCFCFC; border: solid 1px #DFDFDF;">
				<tr style="font-weight: bold;">
					<td height="30" align="center">代码样式</td>
					<td>复制以下代码到指定位置</td>
				</tr>
				<tr>
					<td width="15%" height="40" align="center"><a href="{#$site_url#}" target="_blank" title="{#$site_name#}">{#$site_name#}</a></td>
					<td><textarea name="textarea" id="textarea" rows="1" onmouseover="this.select();" style="border: solid 1px #dadada; font: normal 11px Lucida Grande; width: 98%;" readonly><a href="{#$site_url#}" target="_blank" title="{#$site_title#}">{#$site_name#}</a></textarea></td>
				</tr>
				<tr>
					<td height="60" align="center"><a href="{#$site_url#}" target="_blank"><img src="{#$site_url#}logo.gif" align="absmiddle" border="0" height="31" width="88" alt="{#$site_name#}" /></a></td>
					<td><textarea name="textarea" id="textarea" rows="2" onmouseover="this.select();" style="border: solid 1px #dadada; font: normal 11px Lucida Grande; width: 98%;" readonly><a href="{#$site_url#}" target="_blank"><img src="{#$site_url#}logo.gif" align="absmiddle" border="0" height="31" width="88" alt="{#$site_name#}" /></a></textarea></td>
				</tr>
			</table>
			</div>
            
            {#if $cfg.is_enabled_submit == 'yes'#}
            <form name="myfrom" id="myfrom" method="post" action="{#$pageurl#}">
            <ul class="formbox">
            	<li><strong>选择分类：</strong><p><select name="cate_id" id="cate_id" required>
            		<option value="">请选择网站分类</option>
            		{#$category_option#}
            	</select> <span style="color: #f00;">*</span></p></li>
                <li><strong>网站域名：</strong><p><input type="text" name="web_url" id="web_url" class="fipt" size="50" maxlength="100" placeholder="请输入网站域名，如：example.com" required />
                <span id="url_msg" style="color: #999; font-size: 12px;">不需要输入http://或www</span></p>
                </li>
                <li><strong>网站名称：</strong><p><input type="text" name="web_name" id="web_name" class="fipt" size="30" maxlength="12" placeholder="请输入网站名称" oninput="checkWebNameLength(this)" required />
                <span id="web_name_msg" style="color: #999; font-size: 12px;">最多12个字符（6个汉字）</span> <span style="color: #f00;">*</span></p></li>
                <li><strong>网站标签：</strong><p><input type="text" name="web_tags" id="web_tags" class="fipt" size="50" maxlength="100" placeholder="请输入网站标签，用逗号分隔" />
                <span style="color: #999; font-size: 12px;">多个标签用英文逗号分隔，如：新闻,资讯,门户</span></p></li>
                <li><strong>网站简介：</strong><p><textarea name="web_intro" id="web_intro" rows="5" cols="60" maxlength="500" placeholder="请输入网站简介，至少10个字符" oninput="updateIntroCount()" onkeyup="updateIntroCount()" required></textarea><br />
            	<span id="intro_msg" style="color: #999; font-size: 12px;">至少10个字符，最多500个字符</span> <span style="color: #f00;">*</span></p></li>
                <li><strong>站长姓名：</strong><p><input type="text" name="web_owner" id="web_owner" class="fipt" size="30" maxlength="20" placeholder="请输入站长姓名" /></p></li>
                <li><strong>电子邮箱：</strong><p><input type="email" name="web_email" id="web_email" class="fipt" size="30" maxlength="50" placeholder="请输入有效的电子邮箱" required /> <span style="color: #f00;">*</span></p></li>
            	<li><strong>验 证 码：</strong><p><input type="text" name="check_code" id="check_code" size="10" maxlength="5" class="fipt" onfocus="refreshimg('mycode');" placeholder="请输入验证码" required /> <span id="mycode">点击输入框即可显示验证码</span> <span style="color: #f00;">*</span></p></li>
            	<li><p><input type="hidden" name="act" value="submit" /><input type="submit" name="submit" value="提交网站" class="fbtn" onclick="return validateForm();" /></p></li>
            </ul>
            </form>
            {#else#}
            <div style="padding: 20px; text-align: center; color: #f00; font-size: 16px;">
                {#$cfg.submit_close_reason#}
            </div>
            {#/if#}
        </div>
    </div>
</div>

<script>
// 网站名称长度检查函数
function checkWebNameLength(input) {
    const value = input.value;
    const msgElement = document.getElementById('web_name_msg');

    // 计算字符长度（中文算2个字符）
    let length = 0;
    for (let i = 0; i < value.length; i++) {
        if (value.charCodeAt(i) > 127) {
            length += 2; // 中文字符算2个字符
        } else {
            length += 1; // 英文字符算1个字符
        }
    }

    if (length > 12) {
        msgElement.innerHTML = '<span style="color: #f00;">网站名称过长！最多12个字符（6个汉字）</span>';
        msgElement.style.color = '#f00';
        return false;
    } else if (length === 0) {
        msgElement.innerHTML = '最多12个字符（6个汉字）';
        msgElement.style.color = '#999';
    } else {
        msgElement.innerHTML = `已输入${length}/12个字符`;
        msgElement.style.color = '#666';
    }
    return true;
}

// 简介字符计数函数
function updateIntroCount() {
    const textarea = document.getElementById('web_intro');
    const msgElement = document.getElementById('intro_msg');
    const length = textarea.value.length;

    if (length < 10) {
        msgElement.innerHTML = `已输入${length}/500个字符，至少需要10个字符`;
        msgElement.style.color = '#f00';
    } else if (length > 500) {
        msgElement.innerHTML = `已输入${length}/500个字符，超出限制！`;
        msgElement.style.color = '#f00';
    } else {
        msgElement.innerHTML = `已输入${length}/500个字符`;
        msgElement.style.color = '#666';
    }
}

// 完整的表单验证函数
function validateForm() {
    // 验证分类选择
    const cateId = document.getElementById('cate_id').value;
    if (!cateId || cateId == '') {
        alert('请选择网站分类！');
        document.getElementById('cate_id').focus();
        return false;
    }

    // 验证网站域名
    const webUrl = document.getElementById('web_url').value.trim();
    if (!webUrl) {
        alert('请输入网站域名！');
        document.getElementById('web_url').focus();
        return false;
    }

    // 域名格式验证 - 支持带路径和查询参数的URL
    const cleanUrl = webUrl.replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, '');

    // 使用更灵活的URL验证，支持路径和查询参数
    let urlToValidate = cleanUrl;
    try {
        // 尝试解析URL以提取主机名
        const tempUrl = new URL('http://' + cleanUrl);
        urlToValidate = tempUrl.hostname;
    } catch (e) {
        // 如果解析失败，使用原始清理后的URL进行基本验证
        urlToValidate = cleanUrl.split('/')[0].split('?')[0];
    }

    // 基本域名格式验证
    const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
    if (!domainPattern.test(urlToValidate)) {
        alert('请输入正确的网站域名格式！');
        document.getElementById('web_url').focus();
        return false;
    }

    // 验证网站名称
    const webName = document.getElementById('web_name').value.trim();
    if (!webName) {
        alert('请输入网站名称！');
        document.getElementById('web_name').focus();
        return false;
    }

    if (!checkWebNameLength(document.getElementById('web_name'))) {
        return false;
    }

    // 验证网站简介
    const webIntro = document.getElementById('web_intro').value.trim();
    if (!webIntro) {
        alert('请输入网站简介！');
        document.getElementById('web_intro').focus();
        return false;
    }

    if (webIntro.length < 10) {
        alert('网站简介至少需要10个字符！');
        document.getElementById('web_intro').focus();
        return false;
    }

    if (webIntro.length > 500) {
        alert('网站简介不能超过500个字符！');
        document.getElementById('web_intro').focus();
        return false;
    }

    // 验证电子邮箱
    const webEmail = document.getElementById('web_email').value.trim();
    if (!webEmail) {
        alert('请输入电子邮箱！');
        document.getElementById('web_email').focus();
        return false;
    }

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(webEmail)) {
        alert('请输入正确的电子邮箱格式！');
        document.getElementById('web_email').focus();
        return false;
    }

    // 验证验证码
    const checkCode = document.getElementById('check_code').value.trim();
    if (!checkCode) {
        alert('请输入验证码！');
        document.getElementById('check_code').focus();
        return false;
    }

    return true;
}

// 表单提交验证
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[name="myfrom"]');
    if (form) {
        // 添加简介字符计数
        const introTextarea = document.getElementById('web_intro');
        if (introTextarea) {
            introTextarea.addEventListener('input', updateIntroCount);
            introTextarea.addEventListener('keyup', updateIntroCount);
        }

        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
        });
    }
});
</script>

{#include file="footer.html"#}
</body>
</html>
