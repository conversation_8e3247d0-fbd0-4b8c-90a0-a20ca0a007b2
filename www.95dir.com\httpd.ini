[ISAPI_Rewrite]

# BEGIN
# 301重定向：强制规范化URL格式，避免重复内容
# 重定向带.html后缀的首页到根目录
RewriteRule ^/index\.html$ / [R=301,L]

# 重定向其他带.html后缀的页面到无后缀版本
RewriteRule ^/(webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)\.html$ /$1 [R=301,L]

# 重定向带尾部斜杠的页面到无斜杠版本（除了首页）
RewriteRule ^/(webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)/$ /$1 [R=301,L]

# 首页、分类浏览、数据归档、最近更新、排行榜、意见反馈
RewriteRule ^/(webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)$ /index.php\?mod=$1 [L]
# 301重定向：规范化最近更新和数据归档URL格式
RewriteRule ^/(update|archives)/(\d+)-(\d+)\.html$ /$1/$2/$3 [R=301,L]
RewriteRule ^/(update|archives)/(\d+)-(\d+)/$ /$1/$2/$3 [R=301,L]
RewriteRule ^/(update|archives)/(\d+)\.html$ /$1/$2 [R=301,L]
RewriteRule ^/(update|archives)/(\d+)/$ /$1/$2 [R=301,L]

# 最近更新和数据归档
RewriteRule ^/(update|archives)/(\d+)/(\d+)$ /index.php\?mod=$1&date=$2&page=$3 [L]
RewriteRule ^/(update|archives)/(\d+)$ /index.php\?mod=$1&date=$2 [L]
# 301重定向：规范化搜索URL格式
RewriteRule ^/search/(name|url|tags|intro)/(.+)-(\d+)\.html$ /search/$1/$2/$3 [R=301,L]
RewriteRule ^/search/(name|url|tags|intro)/(.+)\.html$ /search/$1/$2 [R=301,L]

# 站内搜索 - 注意：搜索页面应该使用noindex，避免被搜索引擎索引
RewriteRule ^/search/(name|url|tags|intro)/(.+)/(\d+)$ /index.php\?mod=search&type=$1&query=$2&page=$3 [L]
RewriteRule ^/search/(name|url|tags|intro)/(.+)$ /index.php\?mod=search&type=$1&query=$2 [L]
# 301重定向：规范化详情页URL格式
RewriteRule ^/(siteinfo|artinfo|linkinfo|diypage)/(\d+)\.html$ /$1/$2 [R=301,L]
RewriteRule ^/(siteinfo|artinfo|linkinfo|diypage)/(\d+)/$ /$1/$2 [R=301,L]

# 站点详细、文章详细、链接详细、单页
RewriteRule ^/siteinfo/(\d+)$ /index.php\?mod=siteinfo&wid=$1 [L]
RewriteRule ^/artinfo/(\d+)$ /index.php\?mod=artinfo&aid=$1 [L]
RewriteRule ^/linkinfo/(\d+)$ /index.php\?mod=linkinfo&lid=$1 [L]
RewriteRule ^/diypage/(\d+)$ /index.php\?mod=diypage&pid=$1 [L]
# 301重定向：规范化RSS和SiteMap URL格式
RewriteRule ^/rssfeed/(.+)/(\d+)\.html$ /rssfeed/$1/$2 [R=301,L]
RewriteRule ^/rssfeed/(\d+)\.html$ /rssfeed/$1 [R=301,L]
RewriteRule ^/sitemap/(\d+)\.html$ /sitemap/$1 [R=301,L]

# RSS和SiteMap
RewriteRule ^/rssfeed/(.+)/(\d+)$ /index.php\?mod=rssfeed&type=$1&cid=$2 [L]
RewriteRule ^/rssfeed/(\d+)$ /index.php\?mod=rssfeed&cid=$1 [L]
RewriteRule ^/sitemap/(\d+)$ /index.php\?mod=sitemap&cid=$1 [L]

# 301重定向：规范化分类目录URL格式
RewriteRule ^/(webdir|article)/(.+)/(\d+)-(\d+)\.html$ /$1/$2/$3/$4 [R=301,L]
RewriteRule ^/(webdir|article)/(.+)/(\d+)-(\d+)/$ /$1/$2/$3/$4 [R=301,L]
RewriteRule ^/(webdir|article)/(.+)/(\d+)\.html$ /$1/$2/$3 [R=301,L]
RewriteRule ^/(webdir|article)/(.+)/(\d+)/$ /$1/$2/$3 [R=301,L]

# 分类目录
RewriteRule ^/(webdir|article)/(.+)/(\d+)/(\d+)$ /index.php\?mod=$1&cid=$3&page=$4 [L]
RewriteRule ^/(webdir|article)/(.+)/(\d+)$ /index.php\?mod=$1&cid=$3 [L]

# END